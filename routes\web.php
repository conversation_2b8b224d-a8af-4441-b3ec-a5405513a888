<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\EmployeeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::middleware(['auth'])->group(function () {

    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::match(['get', 'post'], 'departments/data', [DepartmentController::class, 'data'])->name('departments.data');
    Route::get('departments/trashed', [DepartmentController::class, 'trashed'])->name('departments.trashed');
    Route::match(['get', 'post'], 'departments/trashed/data', [DepartmentController::class, 'trashedData'])->name('departments.trashed.data');
    Route::post('departments/{id}/restore', [DepartmentController::class, 'restore'])->name('departments.restore');
    Route::get('departments/{department}/editdata', [DepartmentController::class, 'showEditData'])->name('departments.showEditData');
    Route::resource('departments', DepartmentController::class);

    Route::match(['get', 'post'], 'employees/data', [EmployeeController::class, 'data'])->name('employees.data');
    Route::get('/employees/trashed', [EmployeeController::class, 'trashed'])->name('employees.trashed');
    Route::match(['get', 'post'], 'employees/trashed/data', [EmployeeController::class, 'trashedData'])->name('employees.trashed.data');
    Route::post('employees/{id}/restore', action: [EmployeeController::class, 'restore'])->name('employees.restore');
    Route::get('employees/{employee}/editdata', [EmployeeController::class, 'showEditData'])->name('employees.showEditData');
    Route::resource('employees', EmployeeController::class);
});

require __DIR__ . '/auth.php';
