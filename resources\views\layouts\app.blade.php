<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <title>{{ config("constants.PROJECT_NAME") }}</title>

  {{-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"> --}}
  <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">

  <link rel="stylesheet" href="https://cdn3.devexpress.com/jslib/23.2.3/css/dx.light.css" />
  {{-- <link rel="stylesheet" href="{{ asset('css/dx.light.css') }}"> --}}

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  {{-- <link rel="stylesheet" href="{{ asset('css/font-awesome.css') }}"> --}}
  @stack('styles')
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">

      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarNav">
        @auth
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                <i class="fas fa-tachometer-alt me-1"></i>
                Dashboard
              </a>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle {{ request()->routeIs('departments.*') ? 'active' : '' }}"
                 href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-building me-1"></i>
                Departments
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ route('departments.index') }}">
                  <i class="fas fa-list me-1"></i>
                  All Departments
                </a></li>

                {{-- create --}}
                {{-- <li><a class="dropdown-item" href="{{ route('departments.index') }}">
                  <i class="fas fa-plus me-1"></i>Add Department</a>
                </li> --}}

              </ul>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle {{ request()->routeIs('employees.*') ? 'active' : '' }}"
                 href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-users me-1"></i>
                Employees
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ route('employees.index') }}">
                  <i class="fas fa-list me-1"></i>
                  All Employees
                </a></li>

                {{-- <li><a class="dropdown-item" href="{{ route('employees.index') }}">
                  <i class="fas fa-user-plus me-1"></i>
                  Add Employee
                </a></li> --}}

              </ul>
            </li>
          </ul>

          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-1"></i>
                {{ Auth::user()->name }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="{{ route('profile.edit') }}">
                  <i class="fas fa-user-edit me-1"></i>
                  Profile
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="dropdown-item">
                      <i class="fas fa-sign-out-alt me-1"></i>
                      Logout
                    </button>
                  </form>
                </li>
              </ul>
            </li>
          </ul>
        @endauth

        @guest
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ route('login') }}">
                <i class="fas fa-sign-in-alt me-1"></i>
                Login
              </a>
            </li>
            @if (Route::has('register'))
              <li class="nav-item">
                <a class="nav-link" href="{{ route('register') }}">
                  <i class="fas fa-user-plus me-1"></i>
                  Register
                </a>
              </li>
            @endif
          </ul>
        @endguest
      </div>
    </div>
  </nav>

  <main class="py-4">
    @if (session('success'))
      <div class="container">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle me-2"></i>
          {{ session('success') }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      </div>
    @endif

    @if (session('error'))
      <div class="container">
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <i class="fas fa-exclamation-circle me-2"></i>
          {{ session('error') }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      </div>
    @endif

    @yield('content')
  </main>
  {{-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script> --}}
  <script src="{{ asset('js/bootstrap.bundle.min.js') }}"></script>

  {{-- <script src="https://cdn3.devexpress.com/jslib/23.2.3/js/dx.all.js"></script> --}}
  <script src="{{ asset('js/dx.all.js') }}"></script>

  @stack('scripts')
</body>
</html>
