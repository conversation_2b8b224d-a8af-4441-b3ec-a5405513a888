<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Support\Facades\Auth;

class AdminTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_department_creation_tracks_admin()
    {
        // Act as the admin user
        $this->actingAs($this->admin);

        // Create a department
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active'
        ]);

        // Assert that admin tracking is set
        $this->assertEquals($this->admin->id, $department->added_admin);
        $this->assertEquals($this->admin->id, $department->updated_admin);
        $this->assertInstanceOf(User::class, $department->addedBy);
        $this->assertInstanceOf(User::class, $department->updatedBy);
    }

    public function test_department_update_tracks_admin()
    {
        // Create another admin user
        $secondAdmin = User::factory()->create([
            'name' => 'Second Admin',
            'email' => '<EMAIL>'
        ]);

        // Act as first admin and create department
        $this->actingAs($this->admin);
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active'
        ]);

        // Act as second admin and update department
        $this->actingAs($secondAdmin);
        $department->update(['name' => 'Updated Department']);

        // Refresh the model
        $department->refresh();

        // Assert that added_admin is still the first admin but updated_admin is the second admin
        $this->assertEquals($this->admin->id, $department->added_admin);
        $this->assertEquals($secondAdmin->id, $department->updated_admin);
    }

    public function test_employee_creation_tracks_admin()
    {
        // Act as the admin user
        $this->actingAs($this->admin);

        // Create a department first
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active'
        ]);

        // Create an employee
        $employee = Employee::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active'
        ]);

        // Assert that admin tracking is set
        $this->assertEquals($this->admin->id, $employee->added_admin);
        $this->assertEquals($this->admin->id, $employee->updated_admin);
        $this->assertInstanceOf(User::class, $employee->addedBy);
        $this->assertInstanceOf(User::class, $employee->updatedBy);
    }

    public function test_employee_update_tracks_admin()
    {
        // Create another admin user
        $secondAdmin = User::factory()->create([
            'name' => 'Second Admin',
            'email' => '<EMAIL>'
        ]);

        // Act as first admin and create employee
        $this->actingAs($this->admin);
        
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active'
        ]);

        $employee = Employee::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active'
        ]);

        // Act as second admin and update employee
        $this->actingAs($secondAdmin);
        $employee->update(['name' => 'Updated Employee']);

        // Refresh the model
        $employee->refresh();

        // Assert that added_admin is still the first admin but updated_admin is the second admin
        $this->assertEquals($this->admin->id, $employee->added_admin);
        $this->assertEquals($secondAdmin->id, $employee->updated_admin);
    }

    public function test_user_relationships_work()
    {
        // Act as the admin user
        $this->actingAs($this->admin);

        // Create some departments and employees
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active'
        ]);

        $employee = Employee::create([
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active'
        ]);

        // Test user relationships
        $this->assertTrue($this->admin->addedDepartments->contains($department));
        $this->assertTrue($this->admin->updatedDepartments->contains($department));
        $this->assertTrue($this->admin->addedEmployees->contains($employee));
        $this->assertTrue($this->admin->updatedEmployees->contains($employee));
    }
}
