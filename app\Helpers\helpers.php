<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;

if (!function_exists('last_query')) {
    function last_query($dump = true)
    {
        $queries = DB::getQueryLog();
        $lastQuery = end($queries);

        if ($dump) {
            echo "<pre>";
            print_r($lastQuery);
            echo "</pre>";
            die;
        }

        return $lastQuery;
    }
}

if (!function_exists('pr')) {
    function pr($data, $die = true)
    {
        echo "<pre>";
        print_r($data);
        echo "</pre>";

        if ($die) {
            die;
        }
    }
}

if (!function_exists('upload_file')) {
    function upload_file($files, string $path, string $fileType = 'file', string $oldFile = null)
    {
        if (!$files) {
            return null;
        }

        if ($files instanceof UploadedFile) {
            if ($oldFile && Storage::disk('public')->exists($path . '/' . $oldFile)) {
                Storage::disk('public')->delete($path . '/' . $oldFile);
            }
            $filename = $fileType . '_' . time() . '_' . uniqid() . '.' . $files->getClientOriginalExtension();
            $files->storeAs($path, $filename, 'public');

            return $filename;
        }

        //$files = is_array($files) ? $files : [$files];
        if (is_array($files)) {
            $uploadedFiles = [];
            foreach ($files as $file) {
                if ($file instanceof UploadedFile) {
                    if ($oldFile && Storage::disk('public')->exists($path . '/' . $oldFile)) {
                        Storage::disk('public')->delete($path . '/' . $oldFile);
                    }

                    $filename = $fileType . '_' . time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                    $file->storeAs($path, $filename, 'public');
                    $uploadedFiles[] = $filename;
                }
            }
            return count($uploadedFiles) === 1 ? $uploadedFiles[0] : $uploadedFiles;
        }

        return null;
    }

}
