/**
 * Simple Message Utility
 * Provides message display functions without jQuery dependency
 */

class MessageUtils {
    /**
     * Show success message
     */
    static showSuccessMessage(message, container = 'body') {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        let targetElement;
        if (container === 'body') {
            targetElement = document.querySelector('main');
            if (targetElement) {
                const containerDiv = document.createElement('div');
                containerDiv.className = 'container';
                containerDiv.innerHTML = alertHtml;
                targetElement.insertBefore(containerDiv, targetElement.firstChild);
            }
        } else {
            targetElement = document.querySelector(container);
            if (targetElement) {
                const alertDiv = document.createElement('div');
                alertDiv.innerHTML = alertHtml;
                targetElement.insertBefore(alertDiv.firstChild, targetElement.firstChild);
            }
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            const successAlerts = document.querySelectorAll('.alert-success');
            successAlerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);
    }

    /**
     * Show error message
     */
    static showErrorMessage(message, container = 'body') {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        let targetElement;
        if (container === 'body') {
            targetElement = document.querySelector('main');
            if (targetElement) {
                const containerDiv = document.createElement('div');
                containerDiv.className = 'container';
                containerDiv.innerHTML = alertHtml;
                targetElement.insertBefore(containerDiv, targetElement.firstChild);
            }
        } else {
            targetElement = document.querySelector(container);
            if (targetElement) {
                const alertDiv = document.createElement('div');
                alertDiv.innerHTML = alertHtml;
                targetElement.insertBefore(alertDiv.firstChild, targetElement.firstChild);
            }
        }

        // Auto-hide after 7 seconds
        setTimeout(() => {
            const errorAlerts = document.querySelectorAll('.alert-danger');
            errorAlerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 7000);
    }

    /**
     * Clear all messages
     */
    static clearMessages() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 500);
        });
    }
}

// Export for use - using both names for compatibility
window.MessageUtils = MessageUtils;
window.ValidationUtils = MessageUtils; // For backward compatibility
