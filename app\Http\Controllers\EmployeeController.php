<?php

namespace App\Http\Controllers;

// use Illuminate\Support\Facades\DB;
// DB::enableQueryLog();

use App\Models\Employee;
use App\Models\Department;
use Illuminate\Http\Request;
use App\Http\Requests\EmployeeRequest;
use Illuminate\Support\Facades\Storage;

class EmployeeController extends Controller
{
    public function index()
    {
        return view('employees.index');
    }

    public function data(Request $request)
    {
        try {
            $query = Employee::with(['department', 'addedBy', 'updatedBy']);

            if ($search = $request->get('search')) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            if ($dept = $request->get('department_id')) {
                $query->where('department_id', $dept);
            }

            $total = $query->count();
            $skip = (int) $request->get('skip', 0);
            $take = (int) $request->get('take', 10);

            $items = $query->orderBy('id', 'desc')->skip($skip)->take($take)->get();

            $items->transform(function ($item) {
                $item->department_name = $item->department?->name;
                $item->added_by_name = $item->addedBy ? $item->addedBy->name : 'N/A';
                $item->updated_by_name = $item->updatedBy ? $item->updatedBy->name : 'N/A';
                return $item;
            });

            return response()->json([
                'data' => $items,
                'totalCount' => $total,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load employees',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        $departments = Department::where('status', 'active')->get();
        //pr($departments->toArray());
        //dd($departments);
        //last_query();
        return view('employees.create', compact('departments'));
    }

    public function store(EmployeeRequest $request)
    {
        try {
            $data = $request->validated();
            if ($request->hasFile('photo')) {
                $data['photo'] = upload_file($request->file('photo'), 'employees/photos', 'photo');
            }
            if ($request->hasFile('resume')) {
                $data['resume'] = upload_file($request->file('resume'), 'employees/docs', 'docs');
            }
            Employee::create($data);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Employee created successfully!'
                ]);
            }

            return redirect()->route('employees.index')->with('success', 'Employee created successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create employee: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to create employee: ' . $e->getMessage())->withInput();
        }
    }


    public function edit(Employee $employee)
    {
        //last_query();
        //pr($employee->toArray());
        $departments = Department::where('status', 'active')->get();
        return view('employees.edit', compact('employee', 'departments'));
    }

    public function update(EmployeeRequest $request, Employee $employee)
    {
        try {
            $data = $request->validated();
            //pr($data);
            if ($request->hasFile('photo')) {
                $data['photo'] = upload_file($request->file('photo'), 'employees/photos', 'photo', $employee->photo);
            }
            if ($request->hasFile('resume')) {
                $data['resume'] = upload_file($request->file('resume'), 'employees/docs', 'docs', $employee->resume);
            }

            $employee->update($data);
            //last_query();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Employee updated successfully!'
                ]);
            }

            return redirect()->route('employees.index')->with('success', 'Employee updated successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update employee: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to update employee: ' . $e->getMessage())->withInput();
        }
    }

    public function destroy(Employee $employee)
    {
        try {
            $employee->delete();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Employee deleted successfully!'
                ]);
            }

            return redirect()->route('employees.index')->with('success', 'Employee deleted successfully!');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete employee: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to delete employee: ' . $e->getMessage());
        }
    }

    public function trashed()
    {
        $employees = Employee::onlyTrashed()->with('department')->orderBy('deleted_at', 'desc')->paginate(10);
        return view('employees.trash', compact('employees'));
    }

    public function trashedData(Request $request)
    {
        try {
            $query = Employee::onlyTrashed()->with('department');
            $sort = $request->get('sort');
            $sortOrder = $request->get('sortOrder', 'asc');

            if ($sort) {
                $query->orderBy($sort, $sortOrder);
            } else {
                $query->orderBy('deleted_at', 'desc');
            }
            $skip = (int) $request->get('skip', 0);
            $take = (int) $request->get('take', 10);
            $total = $query->count();

            $employees = $query->skip($skip)->take($take)->get();

            $items = $employees->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'emp_id' => $employee->emp_id ?? 'N/A',
                    'name' => $employee->name ?? 'N/A',
                    'email' => $employee->email ?? 'N/A',
                    'phone' => $employee->phone ?? 'N/A',
                    'department_name' => $employee->department ? $employee->department->name : 'N/A',
                    'designation' => $employee->designation ?? 'N/A',
                    'status' => $employee->status ?? 'inactive',
                    'photo' => $employee->photo
                        ? Storage::url('employees/photos/' . $employee->photo)
                        : null,
                    'deleted_at' => $employee->deleted_at
                        ? $employee->deleted_at->format('M d, Y H:i A')
                        : 'N/A',
                ];
            });
            //pr($items->toArray());
            $response = [
                'data' => $items,
                'totalCount' => $total,
            ];
            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load trashed employees: ' . $e->getMessage(),
                'data' => [],
                'totalCount' => 0
            ], 500);
        }
    }

    public function restore($id)
    {
        try {
            $employee = Employee::withTrashed()->findOrFail($id);
            $employee->restore();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Employee restored successfully!'
                ]);
            }

            return redirect()->route('employees.trashed')->with('success', 'Employee restored successfully!');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to restore employee: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Failed to restore employee: ' . $e->getMessage());
        }
    }

    public function showEditData(Employee $employee)
    {
        $data = $employee->toArray();

        $data['photo'] = $employee->photo ? (\Storage::url('employees/photos/' . $employee->photo)) : '';
        $data['resume'] = $employee->resume ? (\Storage::url('employees/docs/' . $employee->resume)) : '';
        //pr($data);
        return response()->json($data);
    }
}
