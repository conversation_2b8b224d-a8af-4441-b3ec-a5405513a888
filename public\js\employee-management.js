/**
 * Employee Management jQ<PERSON>y <PERSON>le
 * Handles employee CRUD operations, validation, and UI interactions
 */

class EmployeeManager {
    constructor() {
        this.validator = new ValidationUtils();
        this.searchTimeout = null;
        this.csrfToken = $('meta[name="csrf-token"]').attr('content');
        this.init();
    }

    init() {
        this.setupValidation();
        this.setupEventListeners();
        this.setupInputFormatting();
    }

    /**
     * Setup form validation rules
     */
    setupValidation() {
        const rules = {
            name: [
                { type: 'required' },
                { type: 'maxLength', value: 255 }
            ],
            email: [
                { type: 'required' },
                { type: 'email' },
                { type: 'maxLength', value: 255 }
            ],
            phone: [
                { type: 'phone' },
                { type: 'minLength', value: 9 },
                { type: 'maxLength', value: 13 }
            ],
            designation: [
                { type: 'maxLength', value: 255 }
            ],
            status: [
                { type: 'required' }
            ]
        };

        const messages = {
            name: {
                required: 'Employee name is required.',
                maxLength: 'Name cannot exceed 255 characters.'
            },
            email: {
                required: 'Email address is required.',
                email: 'Please enter a valid email address.',
                maxLength: 'Email cannot exceed 255 characters.'
            },
            phone: {
                phone: 'Phone number must be 9-13 digits.',
                minLength: 'Phone number must be at least 9 digits.',
                maxLength: 'Phone number cannot exceed 13 digits.'
            },
            designation: {
                maxLength: 'Designation cannot exceed 255 characters.'
            },
            status: {
                required: 'Employee status is required.'
            }
        };

        // Initialize validation for employee forms
        this.validator.init('#employeeForm', rules, messages);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        const self = this;

        // Search functionality with debounce
        $(document).on('input', '#employeeSearch', function() {
            const searchTerm = $(this).val().trim();

            clearTimeout(self.searchTimeout);
            self.searchTimeout = setTimeout(() => {
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    self.filterEmployees();
                }
            }, 300);
        });

        // Department filter change
        $(document).on('change', '#departmentFilter', function() {
            self.filterEmployees();
        });

        // Form submission
        $(document).on('submit', '#employeeForm', function(e) {
            e.preventDefault();
            self.submitForm($(this));
        });

        // Delete confirmation
        $(document).on('click', '.delete-employee', function(e) {
            e.preventDefault();
            const employeeId = $(this).data('id');
            const employeeName = $(this).data('name');
            self.confirmDelete(employeeId, employeeName);
        });

        // Restore employee
        $(document).on('click', '.restore-employee', function(e) {
            e.preventDefault();
            const employeeId = $(this).data('id');
            self.restoreEmployee(employeeId);
        });

        // Permanent delete
        $(document).on('click', '.permanent-delete-employee', function(e) {
            e.preventDefault();
            const employeeId = $(this).data('id');
            const employeeName = $(this).data('name');
            self.confirmPermanentDelete(employeeId, employeeName);
        });
    }

    /**
     * Setup input formatting
     */
    setupInputFormatting() {
        // Format name field (letters only)
        ValidationUtils.formatLettersInput('#name');

        // Format phone field (numbers only) - Enhanced for mobile numbers
        $(document).on('input', '#phone', function() {
            if (this.value) {
                // Remove any non-numeric characters
                this.value = this.value.replace(/[^0-9]/g, '');
                // Limit to 13 digits for mobile numbers
                if (this.value.length > 13) {
                    this.value = this.value.substring(0, 13);
                }
            }
        });

        // Clear errors on input with instant feedback
        $(document).on('input', 'input, select, textarea', function() {
            const field = $(this);
            if (field.hasClass('is-invalid')) {
                // Use the instance method through the validator instance
                if (window.employeeManager && window.employeeManager.validator) {
                    window.employeeManager.validator.clearFieldError(field);
                }
            }
        });

        // Add instant validation feedback for phone field
        $(document).on('blur', '#phone', function() {
            const phoneValue = $(this).val();
            if (phoneValue && phoneValue.length > 0 && phoneValue.length < 9) {
                // Use the instance method through the validator instance
                if (window.employeeManager && window.employeeManager.validator) {
                    window.employeeManager.validator.showFieldError($(this), 'Phone number must be at least 9 digits.');
                }
            }
        });
    }

    /**
     * Filter employees based on search and department
     */
    filterEmployees() {
        if (typeof window.grid !== 'undefined' && window.grid.refresh) {
            window.grid.refresh();
        }
    }

    /**
     * Submit employee form
     */
    submitForm(form) {
        if (!this.validator.validateForm(form)) {
            ValidationUtils.showErrorMessage('Please fix the validation errors before submitting.');
            return;
        }

        const formData = new FormData(form[0]);
        const url = form.attr('action');
        const method = form.find('input[name="_method"]').val() || 'POST';

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(
                        response.message || (method === 'PUT' ? 'Employee updated successfully!' : 'Employee created successfully!')
                    );

                    // Refresh grid if available
                    if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                        window.grid.refresh();
                    }

                    // Close modal or redirect
                    this.closeModal();
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'An error occurred while saving the employee.');
                }
            },
            error: (xhr) => {
                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON?.errors;
                    if (errors) {
                        this.displayValidationErrors(errors);
                    } else {
                        const message = xhr.responseJSON?.message || 'Validation failed.';
                        ValidationUtils.showErrorMessage(message);
                    }
                } else {
                    const errorMessage = xhr.responseJSON?.message || 'An error occurred while saving the employee.';
                    ValidationUtils.showErrorMessage(errorMessage);
                }
            }
        });
    }

    /**
     * Display validation errors from server
     */
    displayValidationErrors(errors) {
        $.each(errors, (field, messages) => {
            const fieldElement = $(`[name="${field}"]`);
            if (fieldElement.length) {
                this.validator.showFieldError(fieldElement, messages[0]);
            }
        });
    }

        /**
         * Confirm delete employee using DevExtreme popup
         */
        confirmDelete(employeeId, employeeName) {
            // Remove any existing popup
            if (window.deleteEmployeePopup) {
                window.deleteEmployeePopup.dispose();
                delete window.deleteEmployeePopup;
            }

            const popup = $("<div>").appendTo("body").dxPopup({
                title: "Delete Employee",
                contentTemplate: function() {
                    return `<div>Are you sure you want to delete employee <strong>"${employeeName}"</strong>?</div>`;
                },
                showTitle: true,
                visible: true,
                width: 400,
                height: 'auto',
                dragEnabled: false,
                closeOnOutsideClick: true,
                showCloseButton: true,
                toolbarItems: [
                    {
                        widget: 'dxButton',
                        toolbar: 'bottom',
                        location: 'after',
                        options: {
                            text: 'Cancel',
                            onClick: function() {
                                popup.dxPopup('instance').hide();
                            }
                        }
                    },
                    {
                        widget: 'dxButton',
                        toolbar: 'bottom',
                        location: 'after',
                        options: {
                            text: 'Delete',
                            type: 'danger',
                            onClick: () => {
                                popup.dxPopup('instance').hide();
                                this.deleteEmployee(employeeId);
                            }
                        }
                    }
                ],
                onHidden: function() {
                    popup.dxPopup('instance').dispose();
                    popup.remove();
                    if (window.deleteEmployeePopup) delete window.deleteEmployeePopup;
                }
            });
            window.deleteEmployeePopup = popup;
        }

    /**
     * Delete employee
     */
    deleteEmployee(employeeId) {
        $.ajax({
            url: `/employees/${employeeId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(response.message || 'Employee deleted successfully!');
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'Failed to delete employee.');
                }

                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: (xhr) => {
                const errorMessage = xhr.responseJSON?.message || 'Failed to delete employee.';
                ValidationUtils.showErrorMessage(errorMessage);
            }
        });
    }

    /**
     * Restore employee
     */
    restoreEmployee(employeeId) {
        $.ajax({
            url: `/employees/${employeeId}/restore`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(response.message || 'Employee restored successfully!');
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'Failed to restore employee.');
                }

                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: (xhr) => {
                const errorMessage = xhr.responseJSON?.message || 'Failed to restore employee.';
                ValidationUtils.showErrorMessage(errorMessage);
            }
        });
    }

    /**
     * Confirm permanent delete
     */
    confirmPermanentDelete(employeeId, employeeName) {
        const message = `Are you sure you want to permanently delete employee "${employeeName}"?\n\nThis action cannot be undone!`;
        if (confirm(message)) {
            this.permanentDeleteEmployee(employeeId);
        }
    }

    /**
     * Permanently delete employee
     */
    permanentDeleteEmployee(employeeId) {
        $.ajax({
            url: `/employees/${employeeId}/force-delete`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: () => {
                ValidationUtils.showSuccessMessage('Employee permanently deleted!');
                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: () => {
                ValidationUtils.showErrorMessage('Failed to permanently delete employee.');
            }
        });
    }

    /**
     * Close modal or form
     */
    closeModal() {
        // Close DevExpress popup if exists
        if (typeof window.employeePopup !== 'undefined') {
            window.employeePopup.hide();
        }

        // Reset form
        $('#employeeForm')[0]?.reset();
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    }
}

// Initialize when document is ready (only if jQuery is available)
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        window.employeeManager = new EmployeeManager();
    });
} else {
    // Fallback for when jQuery loads later
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                window.employeeManager = new EmployeeManager();
            });
        }
    });
}
