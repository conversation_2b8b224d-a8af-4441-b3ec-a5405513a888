<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmployeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $employeeId = $this->route('employee') ? $this->route('employee')->id : null;

        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:employees,email,' . $employeeId,
            'phone' => 'nullable|string|max:30',
            'department_id' => 'nullable|exists:departments,id',
            'designation' => 'nullable|string|max:255',
            'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'resume' => 'nullable|mimes:pdf|max:5120',
            'status' => 'required|in:active,inactive',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Employee name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'department_id.exists' => 'Selected department does not exist.',
            'photo.image' => 'Photo must be an image file.',
            'photo.mimes' => 'Photo must be a JPG, JPEG, or PNG file.',
            'photo.max' => 'Photo size must not exceed 2MB.',
            'resume.mimes' => 'Resume must be a PDF file.',
            'resume.max' => 'Resume size must not exceed 5MB.',
            'status.required' => 'Employee status is required.',
            'status.in' => 'Status must be either active or inactive.',
        ];
    }
}
