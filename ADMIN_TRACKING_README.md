# Admin Tracking Feature

This document explains the admin tracking functionality that has been added to the Employee-Department Management system.

## Overview

The admin tracking feature automatically tracks which admin user created and last updated each department and employee record. This provides an audit trail for all changes made to the system.

## Database Changes

### New Columns Added

**Departments Table:**
- `added_admin` (nullable, foreign key to users.id) - Tracks who created the department
- `updated_admin` (nullable, foreign key to users.id) - Tracks who last updated the department

**Employees Table:**
- `added_admin` (nullable, foreign key to users.id) - Tracks who created the employee
- `updated_admin` (nullable, foreign key to users.id) - Tracks who last updated the employee

### Foreign Key Constraints

Both tables have foreign key constraints that reference the `users` table. If a user is deleted, the admin tracking fields are set to NULL (ON DELETE SET NULL).

## Model Changes

### Department Model

**New Relationships:**
- `addedBy()` - Returns the User who created the department
- `updatedBy()` - Returns the User who last updated the department

**Automatic Tracking:**
- When creating a department, both `added_admin` and `updated_admin` are set to the current authenticated user
- When updating a department, only `updated_admin` is updated to the current authenticated user

### Employee Model

**New Relationships:**
- `addedBy()` - Returns the User who created the employee
- `updatedBy()` - Returns the User who last updated the employee

**Automatic Tracking:**
- When creating an employee, both `added_admin` and `updated_admin` are set to the current authenticated user
- When updating an employee, only `updated_admin` is updated to the current authenticated user

### User Model

**New Relationships:**
- `addedDepartments()` - Returns all departments created by this user
- `updatedDepartments()` - Returns all departments last updated by this user
- `addedEmployees()` - Returns all employees created by this user
- `updatedEmployees()` - Returns all employees last updated by this user

## Controller Changes

### Department Controller

The `data()` method now includes admin information:
- Loads `addedBy` and `updatedBy` relationships
- Adds `added_by_name` and `updated_by_name` to the response

### Employee Controller

The `data()` method now includes admin information:
- Loads `addedBy` and `updatedBy` relationships
- Adds `added_by_name` and `updated_by_name` to the response

## Usage Examples

### Creating Records

```php
// Login as an admin user
Auth::login($admin);

// Create a department - admin tracking is automatic
$department = Department::create([
    'name' => 'IT Department',
    'code' => 'IT',
    'status' => 'active'
]);

// Both added_admin and updated_admin will be set to the current user's ID
echo $department->addedBy->name; // Admin's name
echo $department->updatedBy->name; // Admin's name
```

### Updating Records

```php
// Login as a different admin user
Auth::login($anotherAdmin);

// Update the department - only updated_admin changes
$department->update(['name' => 'Information Technology']);

echo $department->addedBy->name; // Original admin's name
echo $department->updatedBy->name; // Current admin's name
```

### Querying Admin Activities

```php
// Get all departments created by a specific admin
$admin = User::find(1);
$createdDepartments = $admin->addedDepartments;

// Get all employees updated by a specific admin
$updatedEmployees = $admin->updatedEmployees;

// Get department with admin information
$department = Department::with(['addedBy', 'updatedBy'])->find(1);
echo "Created by: " . $department->addedBy->name;
echo "Last updated by: " . $department->updatedBy->name;
```

## Testing

Run the test suite to verify the admin tracking functionality:

```bash
php artisan test tests/Feature/AdminTrackingTest.php
```

## Demo

You can run a demonstration of the admin tracking functionality:

```bash
php artisan demo:admin-tracking
```

This command will:
1. Create sample records with admin tracking
2. Switch to a different admin and update the records
3. Show how the tracking changes
4. Display relationship queries
5. Clean up the demo data

## Migration Commands

The following migrations were created:

1. `2025_09_23_000001_add_admin_tracking_to_departments_table.php` - Adds admin tracking columns to departments
2. `2025_09_23_000002_add_admin_tracking_to_employees_table.php` - Adds admin tracking columns to employees
3. `2025_09_23_000003_update_existing_records_with_admin_tracking.php` - Updates existing records with admin tracking

To apply these migrations:

```bash
php artisan migrate
```

## Important Notes

1. **Authentication Required**: Admin tracking only works when a user is authenticated. If no user is logged in, the admin tracking fields will be NULL.

2. **Automatic Tracking**: The tracking is handled automatically in the model's boot method. You don't need to manually set these fields in your controllers.

3. **Existing Data**: The migration automatically assigns the first admin user to existing records. You may want to customize this logic based on your needs.

4. **Soft Deletes**: The admin tracking works with soft deletes. When records are restored, the updated_admin field will be set to the user who performed the restore.

5. **API Responses**: The controller methods now include admin names in their JSON responses for easy display in the frontend.

## Frontend Integration

The admin tracking information is now available in the API responses:

```json
{
  "data": [
    {
      "id": 1,
      "name": "IT Department",
      "code": "IT",
      "status": "active",
      "added_by_name": "Admin User",
      "updated_by_name": "Test User",
      "created_at": "2025-09-23T05:54:11.000000Z",
      "updated_at": "2025-09-23T05:54:15.000000Z"
    }
  ]
}
```

You can display this information in your frontend tables or forms to show users who created and last modified each record.
