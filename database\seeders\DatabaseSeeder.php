<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin users first
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        $testUser = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Login as admin to enable admin tracking
        Auth::login($admin);

        $departments = [
            ['name' => 'Human Resources', 'code' => 'HR', 'status' => 'active'],
            ['name' => 'Information Technology', 'code' => 'IT', 'status' => 'active'],
            ['name' => 'Finance', 'code' => 'FIN', 'status' => 'active'],
            ['name' => 'Marketing', 'code' => 'MKT', 'status' => 'active'],
            ['name' => 'Operations', 'code' => 'OPS', 'status' => 'active'],
        ];

        foreach ($departments as $dept) {
            Department::create($dept);
        }

        $employees = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '******-0101',
                'department_id' => 1, // HR
                'designation' => 'HR Manager',
                'status' => 'active'
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '******-0102',
                'department_id' => 2, // IT
                'designation' => 'Software Developer',
                'status' => 'active'
            ],
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'phone' => '******-0103',
                'department_id' => 3, // Finance
                'designation' => 'Financial Analyst',
                'status' => 'active'
            ],
            [
                'name' => 'Sarah Wilson',
                'email' => '<EMAIL>',
                'phone' => '******-0104',
                'department_id' => 4, // Marketing
                'designation' => 'Marketing Specialist',
                'status' => 'active'
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'phone' => '******-0105',
                'department_id' => 2, // IT
                'designation' => 'System Administrator',
                'status' => 'active'
            ],
        ];

        foreach ($employees as $emp) {
            Employee::create($emp);
        }
    }
}
