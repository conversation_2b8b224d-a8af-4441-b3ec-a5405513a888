{"version": 2, "defects": [], "times": {"Tests\\Feature\\AdminTrackingTest::test_department_creation_tracks_admin": 0.039, "Tests\\Feature\\AdminTrackingTest::test_department_update_tracks_admin": 0.008, "Tests\\Feature\\AdminTrackingTest::test_employee_creation_tracks_admin": 0.009, "Tests\\Feature\\AdminTrackingTest::test_employee_update_tracks_admin": 0.009, "Tests\\Feature\\AdminTrackingTest::test_user_relationships_work": 0.012, "Tests\\Feature\\DepartmentEmployeeValidationTest::it_prevents_deleting_department_with_active_employees": 0.099, "Tests\\Feature\\DepartmentEmployeeValidationTest::it_allows_deleting_department_without_employees": 0.013, "Tests\\Feature\\DepartmentEmployeeValidationTest::it_checks_employees_endpoint_returns_correct_data": 0.014, "Tests\\Feature\\DepartmentEmployeeValidationTest::it_prevents_deleting_department_with_soft_deleted_employees": 0.013}}