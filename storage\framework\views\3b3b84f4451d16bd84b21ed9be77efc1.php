<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <title><?php echo e(config("constants.PROJECT_NAME")); ?></title>

  
  <link rel="stylesheet" href="<?php echo e(asset('css/bootstrap.min.css')); ?>">

  <link rel="stylesheet" href="https://cdn3.devexpress.com/jslib/23.2.3/css/dx.light.css" />
  

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">

      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarNav">
        <?php if(auth()->guard()->check()): ?>
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                <i class="fas fa-tachometer-alt me-1"></i>
                Dashboard
              </a>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('departments.*') ? 'active' : ''); ?>"
                 href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-building me-1"></i>
                Departments
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo e(route('departments.index')); ?>">
                  <i class="fas fa-list me-1"></i>
                  All Departments
                </a></li>

                
                

              </ul>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('employees.*') ? 'active' : ''); ?>"
                 href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-users me-1"></i>
                Employees
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo e(route('employees.index')); ?>">
                  <i class="fas fa-list me-1"></i>
                  All Employees
                </a></li>

                

              </ul>
            </li>
          </ul>

          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-1"></i>
                <?php echo e(Auth::user()->name); ?>

              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="<?php echo e(route('profile.edit')); ?>">
                  <i class="fas fa-user-edit me-1"></i>
                  Profile
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="dropdown-item">
                      <i class="fas fa-sign-out-alt me-1"></i>
                      Logout
                    </button>
                  </form>
                </li>
              </ul>
            </li>
          </ul>
        <?php endif; ?>

        <?php if(auth()->guard()->guest()): ?>
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="<?php echo e(route('login')); ?>">
                <i class="fas fa-sign-in-alt me-1"></i>
                Login
              </a>
            </li>
            <?php if(Route::has('register')): ?>
              <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('register')); ?>">
                  <i class="fas fa-user-plus me-1"></i>
                  Register
                </a>
              </li>
            <?php endif; ?>
          </ul>
        <?php endif; ?>
      </div>
    </div>
  </nav>

  <main class="py-4">
    <?php if(session('success')): ?>
      <div class="container">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle me-2"></i>
          <?php echo e(session('success')); ?>

          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
      <div class="container">
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <i class="fas fa-exclamation-circle me-2"></i>
          <?php echo e(session('error')); ?>

          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      </div>
    <?php endif; ?>

    <?php echo $__env->yieldContent('content'); ?>
  </main>
  
  <script src="<?php echo e(asset('js/bootstrap.bundle.min.js')); ?>"></script>

  
  <script src="<?php echo e(asset('js/dx.all.js')); ?>"></script>

  <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\employee-department-manage\resources\views/layouts/app.blade.php ENDPATH**/ ?>