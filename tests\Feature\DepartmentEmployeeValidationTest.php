<?php

namespace Tests\Feature;

use App\Models\Department;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DepartmentEmployeeValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_prevents_deleting_department_with_active_employees()
    {
        // Create a department
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Create an employee in this department
        Employee::create([
            'emp_id' => 'EMP001',
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Attempt to delete the department
        $response = $this->actingAs($this->user)
            ->deleteJson("/departments/{$department->id}");

        // Should return error
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
            ])
            ->assertJsonFragment([
                'message' => "Cannot delete department 'Test Department' because it has 1 employee(s) assigned to it. Please reassign or remove the employees first."
            ]);

        // Department should still exist
        $this->assertDatabaseHas('departments', ['id' => $department->id]);
    }

    /** @test */
    public function it_allows_deleting_department_without_employees()
    {
        // Create a department without employees
        $department = Department::create([
            'name' => 'Empty Department',
            'code' => 'EMPTY',
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Attempt to delete the department
        $response = $this->actingAs($this->user)
            ->deleteJson("/departments/{$department->id}");

        // Should succeed
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Department deleted successfully!'
            ]);

        // Department should be soft deleted
        $this->assertSoftDeleted('departments', ['id' => $department->id]);
    }

    /** @test */
    public function it_checks_employees_endpoint_returns_correct_data()
    {
        // Create a department
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Create employees in this department
        Employee::create([
            'emp_id' => 'EMP001',
            'name' => 'Active Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        $inactiveEmployee = Employee::create([
            'emp_id' => 'EMP002',
            'name' => 'Inactive Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'inactive',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Soft delete one employee
        $inactiveEmployee->delete();

        // Check employees endpoint
        $response = $this->actingAs($this->user)
            ->getJson("/departments/{$department->id}/check-employees");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'hasEmployees' => true,
                'totalEmployees' => 2, // Including soft deleted
                'activeEmployees' => 1, // Only active ones
            ]);
    }

    /** @test */
    public function it_prevents_deleting_department_with_soft_deleted_employees()
    {
        // Create a department
        $department = Department::create([
            'name' => 'Test Department',
            'code' => 'TEST',
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);

        // Create and soft delete an employee
        $employee = Employee::create([
            'emp_id' => 'EMP001',
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active',
            'added_admin' => $this->user->id,
            'updated_admin' => $this->user->id,
        ]);
        
        $employee->delete(); // Soft delete

        // Attempt to delete the department
        $response = $this->actingAs($this->user)
            ->deleteJson("/departments/{$department->id}");

        // Should still prevent deletion because of soft deleted employee
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
            ]);

        // Department should still exist
        $this->assertDatabaseHas('departments', ['id' => $department->id]);
    }
}
