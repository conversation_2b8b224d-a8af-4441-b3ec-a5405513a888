/**
 * Lightweight jQuery Validation Utility
 * Provides reusable validation functions for forms
 */

class ValidationUtils {
    constructor() {
        this.rules = {};
        this.messages = {};
    }

    /**
     * Initialize validation for a form
     * @param {string} formSelector - jQuery selector for the form
     * @param {object} rules - Validation rules
     * @param {object} messages - Custom error messages
     */
    init(formSelector, rules = {}, messages = {}) {
        this.rules = rules;
        this.messages = messages;
        this.bindEvents(formSelector);
    }

    /**
     * Bind validation events to form inputs
     */
    bindEvents(formSelector) {
        const self = this;

        // Real-time validation on input/blur
        $(formSelector).find('input, select, textarea').on('input blur', function() {
            const field = $(this);
            const fieldName = field.attr('name');

            if (self.rules[fieldName]) {
                self.validateField(field, fieldName);
            }
        });

        // Form submission validation
        $(formSelector).on('submit', function(e) {
            if (!self.validateForm($(this))) {
                e.preventDefault();
                return false;
            }
        });
    }

    /**
     * Validate a single field
     */
    validateField(field, fieldName) {
        const value = field.val() ? field.val().trim() : '';
        const rules = this.rules[fieldName];
        let isValid = true;
        let errorMessage = '';

        // Clear previous errors
        this.clearFieldError(field);

        // Check each rule
        for (const rule of rules) {
            const result = this.applyRule(value, rule, field);
            if (!result.valid) {
                isValid = false;
                errorMessage = this.messages[fieldName]?.[rule.type] || result.message;
                break;
            }
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * Validate entire form
     */
    validateForm(form) {
        let isValid = true;
        const self = this;

        form.find('input, select, textarea').each(function() {
            const field = $(this);
            const fieldName = field.attr('name');

            if (self.rules[fieldName]) {
                if (!self.validateField(field, fieldName)) {
                    isValid = false;
                }
            }
        });

        return isValid;
    }

    /**
     * Apply validation rule
     */
    applyRule(value, rule, field) {
        switch (rule.type) {
            case 'required':
                return {
                    valid: value.length > 0,
                    message: 'This field is required.'
                };

            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return {
                    valid: !value || emailRegex.test(value),
                    message: 'Please enter a valid email address.'
                };

            case 'minLength':
                return {
                    valid: !value || value.length >= rule.value,
                    message: `Minimum ${rule.value} characters required.`
                };

            case 'maxLength':
                return {
                    valid: !value || value.length <= rule.value,
                    message: `Maximum ${rule.value} characters allowed.`
                };

            case 'numeric':
                return {
                    valid: !value || /^\d+$/.test(value),
                    message: 'Only numbers are allowed.'
                };

            case 'alphanumeric':
                return {
                    valid: !value || /^[a-zA-Z0-9]+$/.test(value),
                    message: 'Only letters and numbers are allowed.'
                };

            case 'phone':
                return {
                    valid: !value || /^\d{9,13}$/.test(value),
                    message: 'Phone number must be 9-13 digits.'
                };

            case 'unique':
                // This would need AJAX validation in real implementation
                return { valid: true, message: '' };

            default:
                return { valid: true, message: '' };
        }
    }

    /**
     * Show field error
     */
    showFieldError(field, message) {
        field.addClass('is-invalid');

        // Remove existing error message
        field.siblings('.invalid-feedback').remove();

        // Add new error message
        field.after(`<div class="invalid-feedback">${message}</div>`);
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.removeClass('is-invalid');
        field.siblings('.invalid-feedback').remove();
    }

    /**
     * Format input as numbers only
     */
    static formatNumericInput(selector) {
        $(selector).on('input', function() {
            if (this.value) {
                this.value = this.value.replace(/[^0-9]/g, '');
            }
        });
    }

    /**
     * Format input as alphanumeric only
     */
    static formatAlphanumericInput(selector) {
        $(selector).on('input', function() {
            if (this.value) {
                this.value = this.value.replace(/[^a-zA-Z0-9]/g, '');
            }
        });
    }

    /**
     * Format input as letters only
     */
    static formatLettersInput(selector) {
        $(selector).on('input', function() {
            if (this.value) {
                this.value = this.value.replace(/[^a-zA-Z.\s]/g, '');
            }
        });
    }

    /**
     * Show success message
     */
    static showSuccessMessage(message, container = 'body') {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        let targetElement;
        if (container === 'body') {
            targetElement = document.querySelector('main');
            if (targetElement) {
                const containerDiv = document.createElement('div');
                containerDiv.className = 'container';
                containerDiv.innerHTML = alertHtml;
                targetElement.insertBefore(containerDiv, targetElement.firstChild);
            }
        } else {
            targetElement = document.querySelector(container);
            if (targetElement) {
                const alertDiv = document.createElement('div');
                alertDiv.innerHTML = alertHtml;
                targetElement.insertBefore(alertDiv.firstChild, targetElement.firstChild);
            }
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            const successAlerts = document.querySelectorAll('.alert-success');
            successAlerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 5000);
    }

    /**
     * Show error message
     */
    static showErrorMessage(message, container = 'body') {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        let targetElement;
        if (container === 'body') {
            targetElement = document.querySelector('main');
            if (targetElement) {
                const containerDiv = document.createElement('div');
                containerDiv.className = 'container';
                containerDiv.innerHTML = alertHtml;
                targetElement.insertBefore(containerDiv, targetElement.firstChild);
            }
        } else {
            targetElement = document.querySelector(container);
            if (targetElement) {
                const alertDiv = document.createElement('div');
                alertDiv.innerHTML = alertHtml;
                targetElement.insertBefore(alertDiv.firstChild, targetElement.firstChild);
            }
        }

        // Auto-hide after 7 seconds
        setTimeout(() => {
            const errorAlerts = document.querySelectorAll('.alert-danger');
            errorAlerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            });
        }, 7000);
    }

    /**
     * Clear all messages
     */
    static clearMessages() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 500);
        });
    }
}

// Export for use
window.ValidationUtils = ValidationUtils;
