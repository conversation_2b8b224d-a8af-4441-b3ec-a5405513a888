<?php

namespace App\Http\Controllers;

use App\Models\Department;
use Illuminate\Http\Request;
use App\Http\Requests\DepartmentRequest;

class DepartmentController extends Controller
{

    public function index()
    {
        //$proName = config("constants.PROJECT_NAME");
        return view('departments.index');
    }

    public function data(Request $request)
    {
        try {
            $query = Department::with(['addedBy', 'updatedBy']);

            if ($search = $request->get('search')) {
                $query->where('name', 'like', "%{$search}%");
            }

            $total = $query->count();
            $skip = (int) $request->get('skip', 0);
            $take = (int) $request->get('take', 10);

            $items = $query->orderBy('id', 'desc')
                ->skip($skip)->take($take)->get();

            $items->transform(function ($item) {
                $item->added_by_name = $item->addedBy ? $item->addedBy->name : 'N/A';
                $item->updated_by_name = $item->updatedBy ? $item->updatedBy->name : 'N/A';
                return $item;
            });
            //pr($items->toArray());
            return response()->json([
                'data' => $items,
                'totalCount' => $total,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load departments',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        //echo "Sam";die;
        return view('departments.create');
    }

    public function store(DepartmentRequest $request)
    {
        $validated = $request->validated();
        $departmentData = [
            'name' => $validated['name'],
            'code' => $validated['code'],
            'status' => $validated['status'],
        ];
        //pr($departmentData);
        Department::create($departmentData);
        return redirect()->route('departments.index')->with('success', 'Department created');
    }

    public function store_copy(DepartmentRequest $request)
    {
        //pr($request->all());
        Department::create($request->validated());
        return redirect()->route('departments.index')->with('success', 'Department created');
    }

    public function edit(Department $department)
    {
        //pr($department->toArray());
        return view('departments.edit', compact('department'));
    }

    public function showEditData(Department $department)
    {
        return response()->json($department);
    }

    public function update(DepartmentRequest $request, Department $department)
    {
        //pr($department->toArray());
        $department->update($request->validated());
        return redirect()->route('departments.index')->with('success', 'Department updated');
    }

    public function destroy(Department $department)
    {
        //pr($department->toArray());
        $department->delete();
        //last_query();
        return redirect()->route('departments.index')->with('success', 'Department deleted');
    }

    public function trashed()
    {
        return view('departments.trashed');
    }

    public function trashedData(Request $request)
    {
        try {
            $query = Department::onlyTrashed();

            $skip = $request->get('skip', 0);
            $take = $request->get('take', 10);

            $total = $query->count();
            $items = $query->skip($skip)->take($take)->get()->map(function ($department) {
                return [
                    'id' => $department->id,
                    'name' => $department->name ?? 'N/A',
                    'code' => $department->code ?? 'N/A',
                    'status' => $department->status ?? 'inactive',
                    'deleted_at' => $department->deleted_at ? $department->deleted_at->format('M d, Y H:i A') : 'N/A',
                ];
            });
            //pr($items->toArray());
            return response()->json([
                'data' => $items,
                'totalCount' => $total,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load trashed departments: ' . $e->getMessage()
            ], 500);
        }
    }

    public function restore($id)
    {
        Department::withTrashed()->findOrFail($id)->restore();
        return redirect()->route('departments.trashed')->with('success', 'Department restored');
    }
}
