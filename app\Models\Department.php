<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Department extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['name', 'code', 'status', 'added_admin', 'updated_admin'];

    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_admin');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_admin');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($department) {
            if (Auth::check()) {
                $department->added_admin = Auth::id();
                $department->updated_admin = Auth::id();
            }
        });

        static::updating(function ($department) {
            if (Auth::check()) {
                $department->updated_admin = Auth::id();
            }
        });
    }
}
