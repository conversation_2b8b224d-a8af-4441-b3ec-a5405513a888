<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['emp_id', 'name', 'email', 'phone', 'department_id', 'designation', 'photo', 'resume', 'status', 'added_admin', 'updated_admin'];

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_admin');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_admin');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            $lastId = self::withTrashed()->max('id') + 1;
            $employee->emp_id = 'EMP' . str_pad($lastId, 4, '0', STR_PAD_LEFT);

            if (Auth::check()) {
                $employee->added_admin = Auth::id();
                $employee->updated_admin = Auth::id();
            }
        });

        static::updating(function ($employee) {
            if (Auth::check()) {
                $employee->updated_admin = Auth::id();
            }
        });
    }
}
