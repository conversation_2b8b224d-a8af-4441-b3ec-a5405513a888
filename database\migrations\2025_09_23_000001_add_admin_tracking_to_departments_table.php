<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->unsignedBigInteger('added_admin')->nullable()->after('status');
            $table->unsignedBigInteger('updated_admin')->nullable()->after('added_admin');
            
            // Add foreign key constraints
            $table->foreign('added_admin')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
                
            $table->foreign('updated_admin')
                ->references('id')
                ->on('users')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->dropForeign(['added_admin']);
            $table->dropForeign(['updated_admin']);
            $table->dropColumn(['added_admin', 'updated_admin']);
        });
    }
};
