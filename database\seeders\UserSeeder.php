<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        // Demo users
        for ($i = 1; $i <= 10; $i++) {
            User::create([
                'name' => "Demo User $i",
                'email' => "user$<EMAIL>",
                'password' => Hash::make('password'),
            ]);
        }
    }
}
