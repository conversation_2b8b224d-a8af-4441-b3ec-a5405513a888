<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Support\Facades\Auth;

class DemoAdminTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:admin-tracking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Demonstrate admin tracking functionality for departments and employees';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Admin Tracking Demo ===');
        $this->newLine();

        // Get the first admin user
        $admin = User::first();
        if (!$admin) {
            $this->error('No admin user found. Please run the seeders first.');
            return 1;
        }

        $this->info("Admin User: {$admin->name} (ID: {$admin->id})");
        $this->newLine();

        // Simulate login
        Auth::login($admin);

        $this->info('1. Creating a new department...');
        $department = Department::create([
            'name' => 'Demo Department',
            'code' => 'DEMO',
            'status' => 'active'
        ]);

        $this->line("   Department created with ID: {$department->id}");
        $this->line("   Added by: {$department->addedBy->name} (ID: {$department->added_admin})");
        $this->line("   Updated by: {$department->updatedBy->name} (ID: {$department->updated_admin})");
        $this->newLine();

        $this->info('2. Creating a new employee...');
        $employee = Employee::create([
            'name' => 'Demo Employee',
            'email' => '<EMAIL>',
            'department_id' => $department->id,
            'status' => 'active'
        ]);

        $this->line("   Employee created with ID: {$employee->id}");
        $this->line("   Employee ID: {$employee->emp_id}");
        $this->line("   Added by: {$employee->addedBy->name} (ID: {$employee->added_admin})");
        $this->line("   Updated by: {$employee->updatedBy->name} (ID: {$employee->updated_admin})");
        $this->newLine();

        // Get second admin if exists
        $secondAdmin = User::where('id', '!=', $admin->id)->first();
        if ($secondAdmin) {
            $this->info('3. Switching to second admin and updating records...');
            Auth::login($secondAdmin);
            $this->line("   Now logged in as: {$secondAdmin->name} (ID: {$secondAdmin->id})");
            $this->newLine();

            $this->line('   Updating department...');
            $department->update(['name' => 'Updated Demo Department']);
            $department->refresh();

            $this->line('   Department updated:');
            $this->line("   Added by: {$department->addedBy->name} (ID: {$department->added_admin})");
            $this->line("   Updated by: {$department->updatedBy->name} (ID: {$department->updated_admin})");
            $this->newLine();

            $this->line('   Updating employee...');
            $employee->update(['name' => 'Updated Demo Employee']);
            $employee->refresh();

            $this->line('   Employee updated:');
            $this->line("   Added by: {$employee->addedBy->name} (ID: {$employee->added_admin})");
            $this->line("   Updated by: {$employee->updatedBy->name} (ID: {$employee->updated_admin})");
            $this->newLine();
        }

        $this->info('4. Checking admin relationships...');
        $this->line("   Admin {$admin->name} has:");
        $this->line("   - Added departments: " . $admin->addedDepartments->count());
        $this->line("   - Updated departments: " . $admin->updatedDepartments->count());
        $this->line("   - Added employees: " . $admin->addedEmployees->count());
        $this->line("   - Updated employees: " . $admin->updatedEmployees->count());
        $this->newLine();

        if ($secondAdmin) {
            $this->line("   Admin {$secondAdmin->name} has:");
            $this->line("   - Added departments: " . $secondAdmin->addedDepartments->count());
            $this->line("   - Updated departments: " . $secondAdmin->updatedDepartments->count());
            $this->line("   - Added employees: " . $secondAdmin->addedEmployees->count());
            $this->line("   - Updated employees: " . $secondAdmin->updatedEmployees->count());
            $this->newLine();
        }

        $this->info('5. Cleaning up demo records...');
        $employee->delete();
        $department->delete();
        $this->line('   Demo records deleted.');
        $this->newLine();

        $this->info('=== Demo Complete ===');
        $this->comment('The admin tracking functionality is working correctly!');
        $this->comment('- When creating records, both added_admin and updated_admin are set to the current user');
        $this->comment('- When updating records, only updated_admin is changed to the current user');
        $this->comment('- Relationships work properly for querying admin activities');

        return 0;
    }
}
