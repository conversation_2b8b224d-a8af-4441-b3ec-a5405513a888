@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">

            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-house me-2"></i>
                        Welcome to {{ config("constants.PROJECT_NAME") }}
                        {{-- <p>{{config('app.name')}}</p> --}}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="text-primary mb-2">
                                Hello, {{ Auth::user()->name }}
                            </h4>
                        </div>
                        <div class="col-md-4 text-end">
                            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="fas fa-sign-out-alt me-1"></i>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Departments</h5>
                                    <h2 class="mb-0">{{ \App\Models\Department::count() }}</h2>
                                </div>
                                {{-- <div class="align-self-center">
                                    <i class="fas fa-building fa-2x"></i>
                                </div> --}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Employees</h5>
                                    <h2 class="mb-0">{{ \App\Models\Employee::count() }}</h2>
                                </div>
                                {{-- <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div> --}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('departments.index') }}" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-building fa-2x d-block mb-2"></i>
                                Manage Departments
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('employees.index') }}" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-users fa-2x d-block mb-2"></i>
                                Manage Employees
                            </a>
                        </div>
                        {{-- <div class="col-md-3 mb-3">
                            <a href="{{ route('departments.create') }}" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                                Add Department
                            </a>
                        </div> --}}
                        {{-- <div class="col-md-3 mb-3">
                            <a href="{{ route('employees.create') }}" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                                Add Employee
                            </a>
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{{-- <link rel="stylesheet" href="{{ asset('css/font-awesome.css') }}"> --}}
@endpush
