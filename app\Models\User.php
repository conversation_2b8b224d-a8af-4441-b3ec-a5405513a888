<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    // Relationships for admin tracking
    public function addedDepartments()
    {
        return $this->hasMany(Department::class, 'added_admin');
    }

    public function updatedDepartments()
    {
        return $this->hasMany(Department::class, 'updated_admin');
    }

    public function addedEmployees()
    {
        return $this->hasMany(Employee::class, 'added_admin');
    }

    public function updatedEmployees()
    {
        return $this->hasMany(Employee::class, 'updated_admin');
    }
}
