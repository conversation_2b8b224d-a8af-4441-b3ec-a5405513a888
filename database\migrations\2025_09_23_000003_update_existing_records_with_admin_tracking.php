<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the first admin user (you can modify this logic as needed)
        $adminUser = DB::table('users')->first();
        
        if ($adminUser) {
            // Update existing departments with admin tracking
            DB::table('departments')
                ->whereNull('added_admin')
                ->update([
                    'added_admin' => $adminUser->id,
                    'updated_admin' => $adminUser->id,
                    'updated_at' => now()
                ]);

            // Update existing employees with admin tracking
            DB::table('employees')
                ->whereNull('added_admin')
                ->update([
                    'added_admin' => $adminUser->id,
                    'updated_admin' => $adminUser->id,
                    'updated_at' => now()
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Set admin tracking fields to null for existing records
        DB::table('departments')->update([
            'added_admin' => null,
            'updated_admin' => null
        ]);

        DB::table('employees')->update([
            'added_admin' => null,
            'updated_admin' => null
        ]);
    }
};
