Laravel 10 Project – Employee & Department Management System

🔧 Environment Setup
Set up a new Laravel project using Laravel version 10.
Ensure your development environment is running PHP version 8.3 or higher.

🔐 Authentication
Implement a login system using Laravel Breeze, Jetstream, or Fortify.
Upon successful login, redirect the user to the dashboard.
If login fails due to invalid credentials, display a clear and user-friendly error message.

🏠 Dashboard
Display the logged-in user's name prominently.
Include a logout button that securely ends the session and redirects to the login page.

🗂️ Department Management
Implement full CRUD operations for departments:
List all departments.
Add new departments.
Edit and update department details.
Implement soft delete functionality.

👥 Employee Management
Implement full CRUD operations for employees:
List all employees with pagination.
Add new employees with fields such as name, email, phone number, department, and
designation.
Edit and update employee details.
Optional: Allow uploading of profile photo and resume (PDF).
Implement search and filter functionality by department or employee name.

🎨 UI Guidelines
Use the following Data Grid UI for listing and managing records: DevExpress DataGrid Demo
Enhance your UI using Bootstrap components: Bootstrap Documentation

📤 Submission
Once completed, initialize a Git repository for your project.
Push the code to the GitLab account that will be shared with you upon task completion.

---

php artisan
php artisan list
php artisan key:generate
php artisan migrate:status

composer required laravel/breeze --dev
prp artisan breeze:install
php artisan migrate

npm install
npm run dev
npm run build

php artisan view:clear
php artisan cache:clear
php artisan config:clear
php artisan storage link
php artisan route:list
php artisan route:list --name=employee

ls app/Http/Controllersls app/Http/Controllers
ls -R app/Http/Controllers

php artisan make:seeder EmployeeSeeder
php artisan db:seed
php artisan migrate:fresh --seed
php artisan serve
